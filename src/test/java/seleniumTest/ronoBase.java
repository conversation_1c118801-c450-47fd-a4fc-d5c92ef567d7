package seleniumTest;

import org.testng.annotations.Test;

import java.time.LocalTime;

public class ronoBase extends Rono_Base_condition {

    @Test
    public void everymin() throws InterruptedException {

        driver.get(url);
        sendKeysToElement(phonenumberpath, phoneNumber);
        sendKeysToElement(passwordpath, password);
        clickbutton(loginbutton);
        Thread.sleep(6000);
        Thread.sleep(60000);
        Thread.sleep(60000);
//        driver.get(wingo1);

        try {
            while (true) {
                try {
                    waitForInternet();
                    LocalTime now = LocalTime.now();
                    int currentMinute = now.getMinute();
                    int currentSecond = now.getSecond();
//                    if (minutesToPrint_2.contains(currentMinute) && (currentSecond >= 1 && currentSecond <= 5)) {
                    if ((currentSecond >= 1 && currentSecond <= 5)) {
                        driver.navigate().refresh();
                        Thread.sleep(8000);

                        getresult();
                        checkStatus();
                        getBalance();


                        if (previous_Balance <= Balance) {
                            previous_Balance = Balance;
                        }

                        if (currentresult.equals(previousresult)) {
                            MasterbetAmount = restbetAmount;
                            samecount = samecount + 1;
                            diffcount = 0;
                        } else {
                            MasterbetAmount *= 2;
                            diffcount = diffcount + 1;
                            samecount = 0;
                        }

                        if (MasterbetAmount == 1) {
                            wallE_string = opposite_string(currentresult);
                            wallE_performed = 1;
                            sendMessageRamu1("h1 => " + wallE_string + " => " + wallE_amount);
                        } else {
                            wallE_string = currentresult;
                            wallE_performed = 1;
                            sendMessageRamu1("h1 => " + wallE_string + " => " + wallE_amount);
                        }

//                        layer();

//
                        if ((wallE_amount == 1 && one_1 == 0) || (wallE_amount == 2 && two_1 == 0) || (wallE_amount == 4 && four_1 == 0) || (wallE_amount == 8 && eight_1 == 0) || (wallE_amount == 16 && sixteen_1 == 0) || (wallE_amount == 32 && thirtytwo_1 == 0) || (wallE_amount == 64 && sixtyfour_1 == 0) || (wallE_amount == 128 && onetwentyeight_1 == 0) || (wallE_amount == 256 && twofixsix_1 == 0) || (wallE_amount == 512 && fivetwelve_1 == 0)) {
                            harshini_string = (wallE_string);//
                            harshini_performed = 1;
                            sendMessageRamu2("current => " + harshini_string + " => " + harshini_amount);
                        } else {
                            harshini_string = opposite_string(wallE_string);//
                            harshini_performed = 1;
                            sendMessageRamu2("current => " + harshini_string + " => " + harshini_amount);
                        }
                        layer();
                        pinnadi_1();

                    }


                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
            takeScreenshot();
            sendMessage("Critical error occurred: " + e.getMessage());
            throw new RuntimeException(e);
        }

    }

    private void rest_value() {

        if (harshini_amount == 2) {
            harshini_amount = 1;
            previous_0_result = "B";
            previous_1_result = "S";
            previous_2_result = "B";
            previous_3_result = "S";
            previous_4_result = "B";
            previous_5_result = "S";
            previous_6_result = "B";
            previous_7_result = "S";
            previous_8_result = "B";
            previous_9_result = "S";
        }

    }

    private void previous_result_update() {
        if (previousresult_number == 0) {
            previous_0_result = currentresult;
        }

        if (previousresult_number == 1) {
            previous_1_result = currentresult;
        }
        if (previousresult_number == 2) {
            previous_2_result = currentresult;
        }
        if (previousresult_number == 3) {
            previous_3_result = currentresult;
        }
        if (previousresult_number == 4) {
            previous_4_result = currentresult;
        }
        if (previousresult_number == 5) {
            previous_5_result = currentresult;
        }
        if (previousresult_number == 6) {
            previous_6_result = currentresult;
        }
        if (previousresult_number == 7) {
            previous_7_result = currentresult;
        }
        if (previousresult_number == 8) {
            previous_8_result = currentresult;
        }
        if (previousresult_number == 9) {
            previous_9_result = currentresult;
        }

    }


}
