package seleniumTest;

public class Rono_Base_condition extends Rono_CheckStatus {

    public void layer() throws InterruptedException {
        what_string = (harshini_string);


        if (harshini_amount == 1) {
            bigHero6_string = what_string;
            bigHero6_performed = 1;
            sendMessagealbin("Current =>" + bigHero6_string + " => " + bigHero6_amount);
            on_the_flow(bigHero6_string, bigHero6_amount);
            divi();
            lay1();
        }


//            samecount_one_two(bigHero6_same);


        if (harshini_amount == 2) {
            cars_string = what_string;
            cars_performed = 1;
            sendMessage2("Current =>" + cars_string + " => " + cars_amount);

//            samecount_one_two(cars_same);
        }
        if (harshini_amount == 4 || harshini_amount == 8 || harshini_amount == 16) {
            dune_string = what_string;
            dune_performed = 1;
            sendMessage4("Current =>" + dune_string + " => " + dune_amount);

            lay2();
        }
        if (harshini_amount == 8) {
            coco_string = what_string;
            coco_performed = 1;
            sendMessage8("Current =>" + coco_string + " => " + coco_amount);
//            samecount_one_two(coco_same);
        }
        if (harshini_amount == 16) {
            ratatouille_string = what_string;
            ratatouille_performed = 1;
            sendMessage16("Current =>" + ratatouille_string + " => " + ratatouille_amount);
//            samecount_one_two(ratatouille_same);

        }
        if (harshini_amount == 32) {
            toyStory_string = what_string;
            toyStory_performed = 1;
            sendMessage32("Current =>" + toyStory_string + " => " + toyStory_amount);

        }
        if (harshini_amount == 4) {
            parasite_string = what_string;
            parasite_performed = 1;
            sendMessagekis4("four =>" + parasite_string + " => " + parasite_amount);

        }
        if (harshini_amount == 64) {
            mulan_string = what_string;
            mulan_performed = 1;
            sendMessagekis8("Current =>" + mulan_string + " => " + mulan_amount);

        }


    }

    private void samecount_one_two(int same) throws InterruptedException {

        if (wonderWoman_amount == 1) {
            hercules_string = wonderWoman_string;
            hercules_performed = 1;
            sendMessageRono1("R1 =>" + hercules_string + " => " + hercules_amount);

            if (hercules_amount == 1) {
                django_string = hercules_string;
                django_performed = 1;
                sendMessageRono2("R2 =>" + django_string + " => " + django_amount);
                if (django_amount == 1) {
                    hatefulEight_string = django_string;
                    hatefulEight_performed = 1;
                    sendMessageRono3("R3 =>" + hatefulEight_string + " => " + hatefulEight_amount);

                    if (hatefulEight_amount == 1) {
                        hotelTransylvania_string = hatefulEight_string;
                        hotelTransylvania_performed = 1;
                        sendMessageRono4("R4 =>" + hotelTransylvania_string + " => " + hotelTransylvania_amount);

                        if (hotelTransylvania_amount == 1) {
                            sing_string = hotelTransylvania_string;
                            sing_performed = 1;
                            sendMessageRono5("R5 =>" + sing_string + " => " + sing_amount);
                            if (sing_amount == 1) {
                                beautyAndBeast_string = sing_string;
                                beautyAndBeast_performed = 1;
                                sendMessageRono6("R6 =>" + beautyAndBeast_string + " => " + beautyAndBeast_amount);

                                if (beautyAndBeast_amount == 1) {
                                    minions_string = beautyAndBeast_string;
                                    minions_performed = 1;
                                    sendMessageRono7("R7 =>" + minions_string + " => " + minions_amount);
                                    if (minions_amount == 1) {
                                        madagascar_string = minions_string;
                                        madagascar_performed = 1;
                                        sendMessageRono8("R8 =>" + madagascar_string + " => " + madagascar_amount);

                                        if (madagascar_amount == 1) {
                                            despicableMe_string = madagascar_string;
                                            despicableMe_performed = 1;
                                            sendMessageRono9("R9 =>" + despicableMe_string + " => " + despicableMe_amount);
                                            if (despicableMe_amount == 1) {
                                                iceAge_string = despicableMe_string;
                                                iceAge_performed = 1;
                                                sendMessageRono10("R10 =>" + iceAge_string + " => " + iceAge_amount);

                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }


    }

    void divi() throws InterruptedException {

        if ((bigHero6_amount == 1 && lay1_one == 0) || (bigHero6_amount == 2 && lay1_two == 0) || (bigHero6_amount == 4 && lay1_four == 0) || (bigHero6_amount == 8 && lay1_eight == 0) || (bigHero6_amount == 16 && lay1_sixteen == 0) || (bigHero6_amount == 32 && lay1_thirtytwo == 0) || (bigHero6_amount == 64 && lay1_sixtyfour == 0) || (bigHero6_amount == 128 && lay1_onetwentyeight == 0) || (bigHero6_amount == 256 && lay1_twofixsix == 0) || (bigHero6_amount == 512 && lay1_fivetwelve == 0)) {
            shrek_string = bigHero6_string;
            shrek_performed = 1;
            sendMessageDivi1("Pre =>" + shrek_string + " => " + shrek_amount);
        }else{
            shrek_string = opposite_string(bigHero6_string);
            shrek_performed = 1;
            sendMessageDivi1("Pre =>" + shrek_string + " => " + shrek_amount);
        }

        lay1_pin(shrek_amount);

        if (shrek_amount == 1) {
            lionKing_string = shrek_string;
            lionKing_performed = 1;
            sendMessageDivi2("one =>" + lionKing_string + " => " + lionKing_amount);

            if ((lionKing_amount == 1 && lay2_one == 0) || (lionKing_amount == 2 && lay2_two == 0) || (lionKing_amount == 4 && lay2_four == 0) || (lionKing_amount == 8 && lay2_eight == 0) || (lionKing_amount == 16 && lay2_sixteen == 0) || (lionKing_amount == 32 && lay2_thirtytwo == 0) || (lionKing_amount == 64 && lay2_sixtyfour == 0) || (lionKing_amount == 128 && lay2_onetwentyeight == 0) || (lionKing_amount == 256 && lay2_twofixsix == 0) || (lionKing_amount == 512 && lay2_fivetwelve == 0)) {
                frozen2_string = lionKing_string;
                frozen2_performed = 1;
                sendMessageDivi3("Pre =>" + frozen2_string + " => " + frozen2_amount);
            }else{
                frozen2_string = opposite_string(lionKing_string);
                frozen2_performed = 1;
                sendMessageDivi3("Pre =>" + frozen2_string + " => " + frozen2_amount);
            }
            lay2_pin(frozen2_amount);

            if (frozen2_amount == 1) {
                laLaLand_string = frozen2_string;
                laLaLand_performed = 1;
                sendMessageDivi4("one =>" + laLaLand_string + " => " + laLaLand_amount);

                if ((laLaLand_amount == 1 && lay3_one == 0) || (laLaLand_amount == 2 && lay3_two == 0) || (laLaLand_amount == 4 && lay3_four == 0) || (laLaLand_amount == 8 && lay3_eight == 0) || (laLaLand_amount == 16 && lay3_sixteen == 0) || (laLaLand_amount == 32 && lay3_thirtytwo == 0) || (laLaLand_amount == 64 && lay3_sixtyfour == 0) || (laLaLand_amount == 128 && lay3_onetwentyeight == 0) || (laLaLand_amount == 256 && lay3_twofixsix == 0) || (laLaLand_amount == 512 && lay3_fivetwelve == 0)) {
                    goodfellas_string = laLaLand_string;
                    goodfellas_performed = 1;
                    sendMessageDivi5("Pre =>" + goodfellas_string + " => " + goodfellas_amount);
                }else{
                    goodfellas_string = opposite_string(laLaLand_string);
                    goodfellas_performed = 1;
                    sendMessageDivi5("Pre =>" + goodfellas_string + " => " + goodfellas_amount);
                }
                lay3_pin(goodfellas_amount);

                if (goodfellas_amount == 1) {
                    killBill_string = goodfellas_string;
                    killBill_performed = 1;
                    sendMessageDivi6("one =>" + killBill_string + " => " + killBill_amount);

                    if ((killBill_amount == 1 && lay4_one == 0) || (killBill_amount == 2 && lay4_two == 0) || (killBill_amount == 4 && lay4_four == 0) || (killBill_amount == 8 && lay4_eight == 0) || (killBill_amount == 16 && lay4_sixteen == 0) || (killBill_amount == 32 && lay4_thirtytwo == 0) || (killBill_amount == 64 && lay4_sixtyfour == 0) || (killBill_amount == 128 && lay4_onetwentyeight == 0) || (killBill_amount == 256 && lay4_twofixsix == 0) || (killBill_amount == 512 && lay4_fivetwelve == 0)) {

                        whiplash_string = killBill_string;
                        whiplash_performed = 1;
                        sendMessageDivi7("one =>" + whiplash_string + " => " + whiplash_amount);
                    }else{
                        whiplash_string = opposite_string(killBill_string);
                        whiplash_performed = 1;
                        sendMessageDivi7("one =>" + whiplash_string + " => " + whiplash_amount);
                    }

                    if(whiplash_amount == 1){
                        aladdin_string = whiplash_string;
                        aladdin_performed = 1;
                        sendMessageDivi8("one =>" + aladdin_string + " => " + aladdin_amount);
                    }


                }


            }
        }


      /*  if (brave_amount == 2) {


            if (shrek_amount == 1) {

                if (lionKing_amount == 1) {

                    if (frozen2_amount == 1) {

                        if (laLaLand_amount == 1) {

                            if (goodfellas_amount == 1) {

                                if (killBill_amount == 1) {

                                    if (whiplash_amount == 1) {

                                        if (aladdin_amount == 1) {
                                            encanto_string = aladdin_string;
                                            encanto_performed = 1;
                                            sendMessageDivi9("one =>" + encanto_string + " => " + encanto_amount);
                                            if (encanto_amount == 1) {
                                                tenet_string = encanto_string;
                                                tenet_performed = 1;
                                                sendMessageDivi10("one =>" + tenet_string + " => " + tenet_amount);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }*/


    }

    private void lay1() throws InterruptedException {

//        if (bigHero6_same == 1 || bigHero6_amount == 2) {
        if (/*bigHero6_same == 1 ||*/ bigHero6_amount == 2) {

//            if (brave_amount == 1 && bigHero6_amount == 2 || bigHero6_same == 1) {
            brave_string = bigHero6_string;
            brave_performed = 1;
            sendMessageRamu2("Current =>" + brave_string + " => " + brave_amount);


            if (brave_amount == 1) {
                hobbit_string = brave_string;
                hobbit_performed = 1;
                sendMessagehar1("Current =>" + hobbit_string + " => " + hobbit_amount);

                if (hobbit_amount == 1) {
                    batman_string = hobbit_string;
                    batman_performed = 1;
                    sendMessagehar2("Current =>" + batman_string + " => " + batman_amount);
                    if (batman_amount == 1) {
                        superman_string = batman_string;
                        superman_performed = 1;
                        sendMessagehar4("Current =>" + superman_string + " => " + superman_amount);

                        if (superman_amount == 1) {
                            aquaman_string = superman_string;
                            aquaman_performed = 1;
                            sendMessagehar8("Current =>" + aquaman_string + " => " + aquaman_amount);
                            if (aquaman_amount == 1) {
                                wonderWoman_string = aquaman_string;
                                wonderWoman_performed = 1;
                                sendMessagehar16("Current =>" + wonderWoman_string + " => " + wonderWoman_amount);
                                samecount_one_two(wonderWoman_amount);
                            }
                        }
                    }
                }
            }
        }


    }

    private void lay2() throws InterruptedException {

        if (dune_amount < 16) {
            dunkirk_string = dune_string;
            dunkirk_performed = 1;
            sendMessagekis2("Har8 =>" + dunkirk_string + " => " + dunkirk_amount);

        }
     /*   if (lionKing_amount == 4) {
            parasite_string = lionKing_string;
            parasite_performed = 1;
            sendMessagekis4("Need =>" + parasite_string + " => " + parasite_amount);

        }
        if (lionKing_amount == 8) {
            mulan_string = lionKing_string;
            mulan_performed = 1;
            sendMessagekis8("Current =>" + mulan_string + " => " + mulan_amount);

        }
        if (lionKing_amount == 16) {
            cloudyMeatballs_string = lionKing_string;
            cloudyMeatballs_performed = 1;
            sendMessagekis16("Current =>" + cloudyMeatballs_string + " => " + cloudyMeatballs_amount);

        }*/
    }

    private void lay4() {
        if (hercules_amount < 128) {
            emojiMovie_string = hercules_string;
            emojiMovie_performed = 1;
            sendMessagelast2("Current =>" + emojiMovie_string + " => " + emojiMovie_amount);

        }
       /* if (frozen2_amount == 2) {
            smurfs_string = frozen2_string;
            smurfs_performed = 1;
            sendMessagelast4("Current =>" + smurfs_string + " => " + smurfs_amount);

        }
        if (frozen2_amount == 4) {
            megamind_string = frozen2_string;
            megamind_performed = 1;
            sendMessagelast8("Current =>" + megamind_string + " => " + megamind_amount);

        }
        if (frozen2_amount == 8) {
            godfather_string = frozen2_string;
            godfather_performed = 1;
            sendMessagelast16("Current =>" + godfather_string + " => " + godfather_amount);

        }
        if (frozen2_amount == 16) {
            pulpFiction_string = frozen2_string;
            pulpFiction_performed = 1;
            sendMessagelast32("Current =>" + pulpFiction_string + " => " + pulpFiction_amount);

        }*/
    }


    void rono() throws InterruptedException {

        if (harshini_samecount >= 1 && harshini_samecount <= 2) {


        }


    }


}
