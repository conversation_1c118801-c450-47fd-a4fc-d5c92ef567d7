package seleniumTest;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.BeforeTest;

import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

public class Rono_Names extends Rono_Messages {

    public static ChromeDriver driver;
    public static String url = "https://bigmumbaib.com/#/login";

    public static String phonenumberpath = "//input[@placeholder='Please enter the phone number']";
    public static String passwordpath = "(//input[@placeholder='Password'])[1]";
    public static String loginbutton = "(//button[text()='Log in'])[1]";

    public static String phoneNumber = "9952272968";
    public static String password = "Deebiga9345890";

    public static String wingo1 = "https://bigmumbaic.com/#/saasLottery/TrxWinGo?gameCode=TrxWinGo_1M&lottery=TrxWinGo";
//    public static String wingo1 = "https://bigmumbaic.com/#/saasLottery/WinGo?gameCode=WinGo_1M&lottery=WinGo";

    public List<Integer> minutesToPrint_2 = Arrays.asList(0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58);
    public List<Integer> minutesToPrint_4 = Arrays.asList(2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58);
    public List<Integer> minutesToPrint_8 = Arrays.asList(6, 14, 22, 30, 38, 46, 54);
    public List<Integer> minutesToPrint_16 = Arrays.asList(14, 30, 46);

    public static String currentresult;
    public static int currentresult_number;
    public static int previousresult_number;
    public static String previousresult;
    public static String betresult;


    public static double balanceValue;
    public static int Balance;

    public int previous_Balance = 5;
    public int bet_amounts;
    public int multiplier;
    public int max_diff;
    public int upto;

    public static int MasterbetAmount = 1;
    public static int restbetAmount = 1;
    public static int samecount = 0;
    public static int diffcount = 0;

    public static String previous_0_result = "B";
    public static String previous_1_result = "S";
    public static String previous_2_result = "S";
    public static String previous_3_result = "B";
    public static String previous_4_result = "S";
    public static String previous_5_result = "S";
    public static String previous_6_result = "B";
    public static String previous_7_result = "S";
    public static String previous_8_result = "S";
    public static String previous_9_result = "B";


    //    public String[] arr = {"B", "B", "S"};
    public String[] arr = {"B", "S", "S"};
    public int total = arr.length;
    public int index = 0;


    public static int harshini_performed = 0;
    public static String harshini_string;


    public static String what_string;

    public static int cars_performed = 0;

    public static int cars_amount = 1;
    public static int cars_same = 0;
    public static int cars_diff = 0;
    public static String cars_string;

    public static int bigHero6_performed = 0;
    public static String bigHero6_string;
    public static int bigHero6_amount = 1;
    public static int bigHero6_same = 0;
    public static int bigHero6_diff = 0;

    public static int dune_performed = 0;
    public static String dune_string;
    public static int dune_amount = 1;
    public static int dune_same = 0;
    public static int dune_diff = 0;

    public static int coco_performed = 0;
    public static String coco_string;
    public static int coco_amount = 1;
    public static int coco_same = 0;
    public static int coco_diff = 0;

    public static int ratatouille_performed = 0;
    public static String ratatouille_string;
    public static int ratatouille_amount = 1;
    public static int ratatouille_same = 0;
    public static int ratatouille_diff = 0;

    public static int toyStory_performed = 0;
    public static String toyStory_string;
    public static int toyStory_amount = 1;
    public static int toyStory_same = 0;
    public static int toyStory_diff = 0;


    public static int brave_performed = 0;
    public static String brave_string;
    public static int brave_amount = 1;
    public static int brave_same = 0;
    public static int brave_diff = 0;

    public static int hobbit_performed = 0;
    public static String hobbit_string;
    public static int hobbit_amount = 1;
    public static int hobbit_same = 0;
    public static int hobbit_diff = 0;


    public static int batman_performed = 0;
    public static String batman_string;
    public static int batman_amount = 1;
    public static int batman_same = 0;
    public static int batman_diff = 0;


    public static int superman_performed = 0;
    public static String superman_string;
    public static int superman_amount = 1;
    public static int superman_same = 0;
    public static int superman_diff = 0;

    public static int aquaman_performed = 0;
    public static String aquaman_string;
    public static int aquaman_amount = 1;
    public static int aquaman_same = 0;
    public static int aquaman_diff = 0;

    public static int wonderWoman_performed = 0;
    public static String wonderWoman_string;
    public static int wonderWoman_amount = 1;
    public static int wonderWoman_same = 0;
    public static int wonderWoman_diff = 0;


    public static int dunkirk_performed = 0;
    public static String dunkirk_string;
    public static int dunkirk_amount = 1;
    public static int dunkirk_same = 0;
    public static int dunkirk_diff = 0;

    public static int parasite_performed = 0;
    public static String parasite_string;
    public static int parasite_amount = 1;
    public static int parasite_same = 0;
    public static int parasite_diff = 0;

    public static int mulan_performed = 0;
    public static String mulan_string;
    public static int mulan_amount = 1;
    public static int mulan_same = 0;
    public static int mulan_diff = 0;

    public static int cloudyMeatballs_performed = 0;
    public static String cloudyMeatballs_string;
    public static int cloudyMeatballs_amount = 1;
    public static int cloudyMeatballs_same = 0;
    public static int cloudyMeatballs_diff = 0;

    public static int emojiMovie_performed = 0;
    public static String emojiMovie_string;
    public static int emojiMovie_amount = 1;
    public static int emojiMovie_same = 0;
    public static int emojiMovie_diff = 0;

    public static int smurfs_performed = 0;
    public static String smurfs_string;
    public static int smurfs_amount = 1;
    public static int smurfs_same = 0;
    public static int smurfs_diff = 0;


    public static int megamind_performed = 0;
    public static String megamind_string;
    public static int megamind_amount = 1;
    public static int megamind_same = 0;
    public static int megamind_diff = 0;


    public static int godfather_performed = 0;
    public static String godfather_string;
    public static int godfather_amount = 1;
    public static int godfather_same = 0;
    public static int godfather_diff = 0;

    public static int pulpFiction_performed = 0;
    public static String pulpFiction_string;
    public static int pulpFiction_amount = 1;
    public static int pulpFiction_same = 0;
    public static int pulpFiction_diff = 0;

    public static int sing_performed = 0;
    public static String sing_string;
    public static int sing_amount = 1;
    public static int sing_same = 0;
    public static int sing_diff = 0;

    public static int minions_performed = 0;
    public static String minions_string;
    public static int minions_amount = 1;
    public static int minions_same = 0;
    public static int minions_diff = 0;


    public static int madagascar_performed = 0;
    public static String madagascar_string;
    public static int madagascar_amount = 1;
    public static int madagascar_same = 0;
    public static int madagascar_diff = 0;

    public static int despicableMe_performed = 0;
    public static String despicableMe_string;
    public static int despicableMe_amount = 1;
    public static int despicableMe_same = 0;
    public static int despicableMe_diff = 0;

    public static int iceAge_performed = 0;
    public static String iceAge_string;
    public static int iceAge_amount = 1;
    public static int iceAge_same = 0;
    public static int iceAge_diff = 0;

    public static int django_performed = 0;
    public static String django_string;
    public static int django_amount = 1;
    public static int django_same = 0;
    public static int django_diff = 0;

    public static int hatefulEight_performed = 0;
    public static String hatefulEight_string;
    public static int hatefulEight_amount = 1;
    public static int hatefulEight_same = 0;
    public static int hatefulEight_diff = 0;


    public static int hotelTransylvania_performed = 0;
    public static String hotelTransylvania_string;
    public static int hotelTransylvania_amount = 1;
    public static int hotelTransylvania_same = 0;
    public static int hotelTransylvania_diff = 0;

    public static int hercules_performed = 0;
    public static String hercules_string;
    public static int hercules_amount = 1;
    public static int hercules_same = 0;
    public static int hercules_diff = 0;

    public static int beautyAndBeast_performed = 0;
    public static String beautyAndBeast_string;
    public static int beautyAndBeast_amount = 1;
    public static int beautyAndBeast_same = 0;
    public static int beautyAndBeast_diff = 0;


    public static int shrek_performed = 0;
    public static String shrek_string;
    public static int shrek_amount = 1;
    public static int shrek_same = 0;
    public static int shrek_diff = 0;


    public static int lionKing_performed = 0;
    public static String lionKing_string;
    public static int lionKing_amount = 1;
    public static int lionKing_same = 0;
    public static int lionKing_diff = 0;

    public static int frozen2_performed = 0;
    public static String frozen2_string;
    public static int frozen2_amount = 1;
    public static int frozen2_same = 0;
    public static int frozen2_diff = 0;

    public static int laLaLand_performed = 0;
    public static String laLaLand_string;
    public static int laLaLand_amount = 1;
    public static int laLaLand_same = 0;
    public static int laLaLand_diff = 0;

    public static int whiplash_performed = 0;
    public static String whiplash_string;
    public static int whiplash_amount = 1;
    public static int whiplash_same = 0;
    public static int whiplash_diff = 0;


    public static int goodfellas_performed = 0;
    public static String goodfellas_string;
    public static int goodfellas_amount = 1;
    public static int goodfellas_same = 0;
    public static int goodfellas_diff = 0;


    public static int killBill_performed = 0;
    public static String killBill_string;
    public static int killBill_amount = 1;
    public static int killBill_same = 0;
    public static int killBill_diff = 0;


    public static int lordRings_performed = 0;
    public static String lordRings_string;
    public static int lordRings_amount = 1;
    public static int lordRings_same = 0;
    public static int lordRings_diff = 0;

    public static int wallE_performed = 0;
    public static String wallE_string;
    public static int wallE_amount = 1;
    public static int wallE_same = 0;
    public static int wallE_diff = 0;

    public static int findingNemo_performed = 0;
    public static String findingNemo_string;
    public static int findingNemo_amount = 1;
    public static int findingNemo_same = 0;
    public static int findingNemo_diff = 0;

    public static int soul_performed = 0;
    public static String soul_string;
    public static int soul_amount = 1;
    public static int soul_same = 0;
    public static int soul_diff = 0;


    public static int aladdin_performed = 0;
    public static String aladdin_string;
    public static int aladdin_amount = 1;
    public static int aladdin_same = 0;
    public static int aladdin_diff = 0;


    public static int encanto_performed = 0;
    public static String encanto_string;
    public static int encanto_amount = 1;
    public static int encanto_same = 0;
    public static int encanto_diff = 0;


    public static int harshini_amount = 1;
    public static int harshini_samecount = 0;
    public static int harshini_diffcount = 0;

    public static int tenet_performed = 0;
    public static String tenet_string;
    public static int tenet_amount = 1;
    public static int tenet_samecount = 0;
    public static int tenet_diffcount = 0;


    public int one_1 = 0;
    public int two_1 = 0;
    public int four_1 = 0;
    public int eight_1 = 0;
    public int sixteen_1 = 0;
    public int thirtytwo_1 = 0;
    public int sixtyfour_1 = 0;
    public int onetwentyeight_1 = 0;
    public int twofixsix_1 = 0;
    public int fivetwelve_1 = 0;

    public int lay1_one = 0;
    public int lay1_two = 0;
    public int lay1_four = 0;
    public int lay1_eight = 0;
    public int lay1_sixteen = 0;
    public int lay1_thirtytwo = 0;
    public int lay1_sixtyfour = 0;
    public int lay1_onetwentyeight = 0;
    public int lay1_twofixsix = 0;
    public int lay1_fivetwelve = 0;

    public int lay2_one = 0;
    public int lay2_two = 0;
    public int lay2_four = 0;
    public int lay2_eight = 0;
    public int lay2_sixteen = 0;
    public int lay2_thirtytwo = 0;
    public int lay2_sixtyfour = 0;
    public int lay2_onetwentyeight = 0;
    public int lay2_twofixsix = 0;
    public int lay2_fivetwelve = 0;

    public int lay3_one = 0;
    public int lay3_two = 0;
    public int lay3_four = 0;
    public int lay3_eight = 0;
    public int lay3_sixteen = 0;
    public int lay3_thirtytwo = 0;
    public int lay3_sixtyfour = 0;
    public int lay3_onetwentyeight = 0;
    public int lay3_twofixsix = 0;
    public int lay3_fivetwelve = 0;

    public int lay4_one = 0;
    public int lay4_two = 0;
    public int lay4_four = 0;
    public int lay4_eight = 0;
    public int lay4_sixteen = 0;
    public int lay4_thirtytwo = 0;
    public int lay4_sixtyfour = 0;
    public int lay4_onetwentyeight = 0;
    public int lay4_twofixsix = 0;
    public int lay4_fivetwelve = 0;



    public void getresult() {
        int resultindex = 2;
        int previousIndex = 3;
        int betIndex = 4;

        currentresult = getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[2]");
        currentresult_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[1]"));
        previousresult_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[1]"));
        previousresult = getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[2]");
        betresult = getElementText("(//div[@class='van-row'])[" + betIndex + "]/div[5]/div/div[2]");

    }

    public void getresultcolor() {
        int resultindex = 1;
        int previousIndex = 2;

        currentresult = getElementText("(//div[@class='van-col van-col--5']//span)[" + resultindex + "]");
        previousresult = getElementText("(//div[@class='van-col van-col--5']//span)[" + previousIndex + "]");
//        System.out.println(currentresult);
//        System.out.println(previousresult);

        if (currentresult.equals("Small")) {
            currentresult = "S";
        } else {
            currentresult = "B";
        }

        if (previousresult.equals("Small")) {
            previousresult = "S";
        } else {
            previousresult = "B";
        }

    }


    public int getBalance() {
        WebElement balanceElement = driver.findElement(By.xpath("(//div[@class='Wallet__C-balance']//div)[1]"));
        String balanceText = balanceElement.getText().replace("₹", "").replace(",", "");
        balanceValue = Double.parseDouble(balanceText);
        Balance = (int) Math.round(balanceValue);
        return Balance;
    }

    public static String getElementText(String xpath) {
        WebElement element = new WebDriverWait(driver, 10).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
        return element.getText();
    }


    public void waitForInternet() {

        while (!isInternetAvailable()) {

            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

    }

    public String opposite_string(String currentresult) {
        if (currentresult.equals("B")) {
            currentresult = "S";
        } else {
            currentresult = "B";
        }
        return currentresult;

    }

    public boolean isInternetAvailable() {
        try {
            URL url = new URL("https://www.google.com");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            int responseCode = connection.getResponseCode();
            return (200 <= responseCode && responseCode <= 399);
        } catch (IOException e) {
            return false;
        }
    }

    public static int findClosestN(int sum) {
        double n = (-1 + Math.sqrt(1 + 8 * sum)) / 2;
        return (int) Math.floor(n); // take the floor to get the lowest valid n
    }


    public void takeScreenshot() {
        try {
            File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
            screenshot.renameTo(new File("error_screenshot_" + System.currentTimeMillis() + ".png"));
        } catch (Exception e) {
            System.out.println("Failed to take screenshot: " + e.getMessage());
        }
    }

    public static void clickbutton(String xpath) throws InterruptedException {

        WebElement element = new WebDriverWait(driver, 20).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
        element.click();
        Thread.sleep(1000);
    }

    public static void sendKeysToElement(String xpath, String keys) throws InterruptedException {
        WebElement element = new WebDriverWait(driver, 10).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
        Thread.sleep(1000);
        element.sendKeys(keys);
    }

    @BeforeTest
    public static void setup() {
        WebDriverManager.chromedriver().setup();
        ChromeOptions chromeOptions = new ChromeOptions();
        chromeOptions.addArguments("--disable-dev-shm-usage");
        chromeOptions.addArguments("--no-sandbox");
//        chromeOptions.addArguments("--headless");
        chromeOptions.addArguments("disables-gpu");
        chromeOptions.addArguments("--mute-audio");
        chromeOptions.addArguments("--remote-allow-origins=*");
        chromeOptions.setPageLoadStrategy(PageLoadStrategy.EAGER);
        driver = new ChromeDriver(chromeOptions);
        driver.manage().window().maximize();
    }

    public void on_the_flow(String what_string, int amount) throws InterruptedException {

        int little_correction = 4;

        if (previous_Balance <= Balance) {
            previous_Balance = Balance;
            bet_amounts = 5;
            multiplier = 1;
        }


        max_diff = (previous_Balance - Balance);


        Thread.sleep(3000);


        upto();


        if (amount < 128) {
            if (Math.abs(previous_Balance - Balance) <= little_correction) {

                performBet(opposite_string(what_string), bet_amounts);
            } else {
                performBet(opposite_string(what_string), amount * bet_amounts);

            }
        } else {
            performBet(opposite_string(what_string), bet_amounts);

        }
        sendMessageObuli("Pre => " + previous_Balance + " => Cur => " + Balance);
        sendMessageHarshini("Max => " + max_diff + " => upto => " + upto);


    }

    private int upto() {

        if (max_diff == 0) {
            upto = 1;
        }

        if (max_diff >= 1 && max_diff <= 4) {
            upto = 2;

        }
        if (max_diff >= 5 && max_diff <= 8) {
            upto = 4;

        }
        if (max_diff >= 9 && max_diff <= 16) {
            upto = 8;

        }
        if (max_diff >= 17 && max_diff <= 32) {
            upto = 16;

        }
        if (max_diff >= 33 && max_diff <= 64) {
            upto = 32;

        }
        if (max_diff >= 65 && max_diff <= 128) {
            upto = 64;

        }
        if (max_diff >= 129 && max_diff <= 256) {
            upto = 128;

        }
        if (max_diff >= 257 && max_diff <= 512) {
            upto = 256;
        }

        return upto;
    }


    public void performBet(String type, double amount) throws InterruptedException {
        Thread.sleep(2000);
        String typeXPath;
        if (type.equals("B")) {
            Thread.sleep(1000);
            typeXPath = "//div[normalize-space(text())='Big']";
        } else {
            Thread.sleep(1000);
            typeXPath = "//div[text()='Big']/following-sibling::div";
        }


        clickbutton(typeXPath);
        WebElement inputField = driver.findElement(By.xpath("//input[@type='number']"));
        inputField.clear();
        inputField.sendKeys(Keys.BACK_SPACE);
        String updatedBetAmountString = Integer.toString((int) amount);
        driver.findElement(By.xpath("//input[@type='number']")).sendKeys(updatedBetAmountString);
        Thread.sleep(2000);
        driver.findElement(By.xpath("//div[text()='Cancel']/following-sibling::div")).click();
//System.out.println((type.equals("B") ? "Big" : "Small") + " Clicked with amount " + amount);
//sendMessage("Bet placed: " + (type.equals("B") ? "Big" : "Small") + " with amount " + amount);
    }

    public void performBetcolor(String type, double amount) throws InterruptedException {
        Thread.sleep(2000);
        String typeXPath;
        if (type.equals("B")) {
            Thread.sleep(1000);
            typeXPath = "//div[normalize-space(text())='Big']";
        } else {
            Thread.sleep(1000);
            typeXPath = "//div[text()='Big']/following-sibling::div";
        }


        clickbutton(typeXPath);
        WebElement inputField = driver.findElement(By.xpath("//input[@type='number']"));
        inputField.clear();
        inputField.sendKeys(Keys.BACK_SPACE);
        String updatedBetAmountString = Integer.toString((int) amount);
        driver.findElement(By.xpath("//input[@type='number']")).sendKeys(updatedBetAmountString);
        Thread.sleep(2000);
        driver.findElement(By.xpath("//button[@type='button']/following-sibling::button[1]")).click();
//System.out.println((type.equals("B") ? "Big" : "Small") + " Clicked with amount " + amount);
//sendMessage("Bet placed: " + (type.equals("B") ? "Big" : "Small") + " with amount " + amount);
    }

    public void podu(String what_string, int amount) throws InterruptedException {

        int little_correction = 4;

        if (previous_Balance <= Balance) {
            previous_Balance = Balance;
            bet_amounts = 5;
            multiplier = 1;
        } else {
            if (amount == 1) {
                multiplier++;
            }
        }


        max_diff = (previous_Balance - Balance);

        if ((max_diff / 5) == 0) {
            bet_amounts = 5;
        } else {
            bet_amounts = 5 * multiplier;
        }

        sendMessageObuli("Pre => " + previous_Balance + " => Cur => " + Balance);
        sendMessageHarshini("Max => " + (max_diff / 5) + " => Multi => " + multiplier);

        Thread.sleep(3000);


        if (amount < 128) {
            if (Math.abs(previous_Balance - Balance) <= little_correction) {

                performBet(opposite_string(what_string), bet_amounts);
            } else {
                performBet(opposite_string(what_string), amount * bet_amounts);

            }
        } else {
            performBet(opposite_string(what_string), bet_amounts);

        }


    }

    public void pinnadi_1() {

        one_1 = (wallE_amount == 1) ? one_1 + 1 : (wallE_amount == 2) ? 0 : one_1;
        two_1 = (wallE_amount == 2) ? two_1 + 1 : (wallE_amount == 4) ? 0 : two_1;
        four_1 = (wallE_amount == 4) ? four_1 + 1 : (wallE_amount == 8) ? 0 : four_1;
        eight_1 = (wallE_amount == 8) ? eight_1 + 1 : (wallE_amount == 16) ? 0 : eight_1;
        sixteen_1 = (wallE_amount == 16) ? sixteen_1 + 1 : (wallE_amount == 32) ? 0 : sixteen_1;
        thirtytwo_1 = (wallE_amount == 32) ? thirtytwo_1 + 1 : (wallE_amount == 64) ? 0 : thirtytwo_1;
        sixtyfour_1 = (wallE_amount == 64) ? sixtyfour_1 + 1 : (wallE_amount == 128) ? 0 : sixtyfour_1;
        onetwentyeight_1 = (wallE_amount == 128) ? onetwentyeight_1 + 1 : (wallE_amount == 256) ? 0 : onetwentyeight_1;
        twofixsix_1 = (wallE_amount == 256) ? twofixsix_1 + 1 : (wallE_amount == 512) ? 0 : twofixsix_1;
        fivetwelve_1 = (wallE_amount == 512) ? fivetwelve_1 + 1 : (wallE_amount == 1024) ? 0 : fivetwelve_1;

    }

    public void lay1_pin(int amount) {

        lay1_one = (amount == 1) ? lay1_one + 1 : (amount == 2) ? 0 : lay1_one;
        lay1_two = (amount == 2) ? lay1_two + 1 : (amount == 4) ? 0 : lay1_two;
        lay1_four = (amount == 4) ? lay1_four + 1 : (amount == 8) ? 0 : lay1_four;
        lay1_eight = (amount == 8) ? lay1_eight + 1 : (amount == 16) ? 0 : lay1_eight;
        lay1_sixteen = (amount == 16) ? lay1_sixteen + 1 : (amount == 32) ? 0 : lay1_sixteen;
        lay1_thirtytwo = (amount == 32) ? lay1_thirtytwo + 1 : (amount == 64) ? 0 : lay1_thirtytwo;
        lay1_sixtyfour = (amount == 64) ? lay1_sixtyfour + 1 : (amount == 128) ? 0 : lay1_sixtyfour;
        lay1_onetwentyeight = (amount == 128) ? lay1_onetwentyeight + 1 : (amount == 256) ? 0 : lay1_onetwentyeight;
        lay1_twofixsix = (amount == 256) ? lay1_twofixsix + 1 : (amount == 512) ? 0 : lay1_twofixsix;
        lay1_fivetwelve = (amount == 512) ? lay1_fivetwelve + 1 : (amount == 1024) ? 0 : lay1_fivetwelve;

    }

    public void lay2_pin(int amount) {

        lay2_one = (amount == 1) ? lay2_one + 1 : (amount == 2) ? 0 : lay2_one;
        lay2_two = (amount == 2) ? lay2_two + 1 : (amount == 4) ? 0 : lay2_two;
        lay2_four = (amount == 4) ? lay2_four + 1 : (amount == 8) ? 0 : lay2_four;
        lay2_eight = (amount == 8) ? lay2_eight + 1 : (amount == 16) ? 0 : lay2_eight;
        lay2_sixteen = (amount == 16) ? lay2_sixteen + 1 : (amount == 32) ? 0 : lay2_sixteen;
        lay2_thirtytwo = (amount == 32) ? lay2_thirtytwo + 1 : (amount == 64) ? 0 : lay2_thirtytwo;
        lay2_sixtyfour = (amount == 64) ? lay2_sixtyfour + 1 : (amount == 128) ? 0 : lay2_sixtyfour;
        lay2_onetwentyeight = (amount == 128) ? lay2_onetwentyeight + 1 : (amount == 256) ? 0 : lay2_onetwentyeight;
        lay2_twofixsix = (amount == 256) ? lay2_twofixsix + 1 : (amount == 512) ? 0 : lay2_twofixsix;
        lay2_fivetwelve = (amount == 512) ? lay2_fivetwelve + 1 : (amount == 1024) ? 0 : lay2_fivetwelve;

    }

    public void lay3_pin(int amount) {

        lay3_one = (amount == 1) ? lay3_one + 1 : (amount == 2) ? 0 : lay3_one;
        lay3_two = (amount == 2) ? lay3_two + 1 : (amount == 4) ? 0 : lay3_two;
        lay3_four = (amount == 4) ? lay3_four + 1 : (amount == 8) ? 0 : lay3_four;
        lay3_eight = (amount == 8) ? lay3_eight + 1 : (amount == 16) ? 0 : lay3_eight;
        lay3_sixteen = (amount == 16) ? lay3_sixteen + 1 : (amount == 32) ? 0 : lay3_sixteen;
        lay3_thirtytwo = (amount == 32) ? lay3_thirtytwo + 1 : (amount == 64) ? 0 : lay3_thirtytwo;
        lay3_sixtyfour = (amount == 64) ? lay3_sixtyfour + 1 : (amount == 128) ? 0 : lay3_sixtyfour;
        lay3_onetwentyeight = (amount == 128) ? lay3_onetwentyeight + 1 : (amount == 256) ? 0 : lay3_onetwentyeight;
        lay3_twofixsix = (amount == 256) ? lay3_twofixsix + 1 : (amount == 512) ? 0 : lay3_twofixsix;
        lay3_fivetwelve = (amount == 512) ? lay3_fivetwelve + 1 : (amount == 1024) ? 0 : lay3_fivetwelve;

    }

    public void lay4_pin(int amount) {

        lay4_one = (amount == 1) ? lay4_one + 1 : (amount == 2) ? 0 : lay4_one;
        lay4_two = (amount == 2) ? lay4_two + 1 : (amount == 4) ? 0 : lay4_two;
        lay4_four = (amount == 4) ? lay4_four + 1 : (amount == 8) ? 0 : lay4_four;
        lay4_eight = (amount == 8) ? lay4_eight + 1 : (amount == 16) ? 0 : lay4_eight;
        lay4_sixteen = (amount == 16) ? lay4_sixteen + 1 : (amount == 32) ? 0 : lay4_sixteen;
        lay4_thirtytwo = (amount == 32) ? lay4_thirtytwo + 1 : (amount == 64) ? 0 : lay4_thirtytwo;
        lay4_sixtyfour = (amount == 64) ? lay4_sixtyfour + 1 : (amount == 128) ? 0 : lay4_sixtyfour;
        lay4_onetwentyeight = (amount == 128) ? lay4_onetwentyeight + 1 : (amount == 256) ? 0 : lay4_onetwentyeight;
        lay4_twofixsix = (amount == 256) ? lay4_twofixsix + 1 : (amount == 512) ? 0 : lay4_twofixsix;
        lay4_fivetwelve = (amount == 512) ? lay4_fivetwelve + 1 : (amount == 1024) ? 0 : lay4_fivetwelve;

    }


}
